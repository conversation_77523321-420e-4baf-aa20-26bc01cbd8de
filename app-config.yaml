# Minimal base configuration - main config is now in app-config.local.yaml
# This file serves as a fallback for production deployments

app:
  title: ACME Corp. Backstage App
  baseUrl: http://localhost:3000

organization:
  name: ACME Corporation

backend:
  baseUrl: http://localhost:7007
  listen:
    port: 7007
  cors:
    origin: http://localhost:3000
    methods: [GET, HEAD, PATCH, POST, PUT, DELETE]
    credentials: true
  database:
    client: better-sqlite3
    connection: ':memory:'

auth:
  providers:
    guest: {}

# Minimal catalog configuration
catalog:
  rules:
    - allow: [Component, System, API, Resource, Location]
  locations: []

# Basic permission setup
permission:
  enabled: true
